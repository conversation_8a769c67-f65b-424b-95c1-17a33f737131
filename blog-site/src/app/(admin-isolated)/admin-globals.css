@tailwind base;
@tailwind components;
@tailwind utilities;

/* Complete CSS Reset for Admin Interface */
.admin-isolated {
  /* Reset all inherited styles */
  all: initial;
  
  /* Base font and display settings */
  font-family: var(--font-sans), ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  
  /* Layout */
  display: block;
  width: 100vw;
  height: 100vh;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  
  /* Colors - Admin Dark Theme */
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  --card: 222.2 84% 4.9%;
  --card-foreground: 210 40% 98%;
  --popover: 222.2 84% 4.9%;
  --popover-foreground: 210 40% 98%;
  --primary: 217.2 91.2% 59.8%;
  --primary-foreground: 222.2 84% 4.9%;
  --secondary: 217.2 32.6% 17.5%;
  --secondary-foreground: 210 40% 98%;
  --muted: 217.2 32.6% 17.5%;
  --muted-foreground: 215 20.2% 65.1%;
  --accent: 217.2 32.6% 17.5%;
  --accent-foreground: 210 40% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;
  --border: 217.2 32.6% 17.5%;
  --input: 217.2 32.6% 17.5%;
  --ring: 224.3 76.3% 94.1%;
  --radius: 0.5rem;
  
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
}

/* Reset all elements within admin */
.admin-isolated *,
.admin-isolated *::before,
.admin-isolated *::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  border: 0;
  font-size: 100%;
  font: inherit;
  vertical-align: baseline;
}

/* Block elements */
.admin-isolated div,
.admin-isolated header,
.admin-isolated nav,
.admin-isolated main,
.admin-isolated aside,
.admin-isolated section,
.admin-isolated article,
.admin-isolated footer {
  display: block;
}

/* Lists */
.admin-isolated ul,
.admin-isolated ol {
  list-style: none;
}

/* Links */
.admin-isolated a {
  color: inherit;
  text-decoration: none;
}

/* Form elements */
.admin-isolated input,
.admin-isolated button,
.admin-isolated textarea,
.admin-isolated select {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  color: inherit;
  background: transparent;
  border: none;
  outline: none;
}

/* Tables */
.admin-isolated table {
  border-collapse: collapse;
  border-spacing: 0;
}

/* Images */
.admin-isolated img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* Headings */
.admin-isolated h1,
.admin-isolated h2,
.admin-isolated h3,
.admin-isolated h4,
.admin-isolated h5,
.admin-isolated h6 {
  font-size: inherit;
  font-weight: inherit;
  line-height: inherit;
  margin: 0;
}

/* Paragraphs */
.admin-isolated p {
  margin: 0;
}

/* Admin-specific utility classes */
.admin-isolated .admin-container {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.admin-isolated .admin-header {
  flex-shrink: 0;
  border-bottom: 1px solid hsl(var(--border));
  background-color: hsl(var(--card));
}

.admin-isolated .admin-body {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.admin-isolated .admin-sidebar {
  width: 250px;
  flex-shrink: 0;
  background-color: hsl(var(--card));
  border-right: 1px solid hsl(var(--border));
  overflow-y: auto;
}

.admin-isolated .admin-main {
  flex: 1;
  overflow-y: auto;
  background-color: hsl(var(--background));
}

/* Ensure no inheritance from parent styles */
.admin-isolated .admin-reset {
  all: unset;
  display: block;
}

/* Override any potential conflicts */
.admin-isolated * {
  font-family: var(--font-sans) !important;
}

/* Hide any potential main website elements */
.admin-isolated .header,
.admin-isolated .footer,
.admin-isolated .nav-menu,
.admin-isolated .main-content {
  display: none !important;
}

/* Simple Button Styles */
.admin-isolated .btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: 0.375rem;
  border: 1px solid transparent;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  background: transparent;
  color: inherit;
}

.admin-isolated .btn:hover {
  opacity: 0.9;
}

.admin-isolated .btn:focus {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 2px;
}

.admin-isolated .btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.admin-isolated .btn-primary {
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
}

.admin-isolated .btn-secondary {
  background-color: hsl(var(--secondary));
  color: hsl(var(--secondary-foreground));
}

.admin-isolated .btn-outline {
  border-color: hsl(var(--border));
  background-color: transparent;
  color: hsl(var(--foreground));
}

.admin-isolated .btn-outline:hover {
  background-color: hsl(var(--accent));
}

.admin-isolated .btn-ghost {
  background-color: transparent;
  color: hsl(var(--foreground));
}

.admin-isolated .btn-ghost:hover {
  background-color: hsl(var(--accent));
}

.admin-isolated .btn-destructive {
  background-color: hsl(var(--destructive));
  color: hsl(var(--destructive-foreground));
}

.admin-isolated .btn-sm {
  padding: 0.25rem 0.75rem;
  font-size: 0.75rem;
}

.admin-isolated .btn-lg {
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
}

.admin-isolated .btn-icon {
  width: 2.5rem;
  height: 2.5rem;
  padding: 0;
}

.admin-isolated .btn-full {
  width: 100%;
}

/* Simple Card Styles */
.admin-isolated .card {
  background-color: hsl(var(--card));
  border: 1px solid hsl(var(--border));
  border-radius: var(--radius);
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.admin-isolated .card-header {
  padding: 1.5rem 1.5rem 0;
}

.admin-isolated .card-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: hsl(var(--card-foreground));
  margin-bottom: 0.5rem;
}

.admin-isolated .card-description {
  font-size: 0.875rem;
  color: hsl(var(--muted-foreground));
}

.admin-isolated .card-content {
  padding: 1.5rem;
}

.admin-isolated .card-footer {
  padding: 0 1.5rem 1.5rem;
}

/* Simple Badge Styles */
.admin-isolated .badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: 0.25rem;
  border: 1px solid transparent;
}

.admin-isolated .badge-default {
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
}

.admin-isolated .badge-secondary {
  background-color: hsl(var(--secondary));
  color: hsl(var(--secondary-foreground));
}

.admin-isolated .badge-outline {
  border-color: hsl(var(--border));
  background-color: transparent;
  color: hsl(var(--foreground));
}

/* Simple Input Styles */
.admin-isolated .input {
  display: flex;
  width: 100%;
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  border: 1px solid hsl(var(--border));
  border-radius: 0.375rem;
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  transition: border-color 0.2s ease;
}

.admin-isolated .input:focus {
  outline: none;
  border-color: hsl(var(--ring));
  box-shadow: 0 0 0 2px hsl(var(--ring) / 0.2);
}

.admin-isolated .input::placeholder {
  color: hsl(var(--muted-foreground));
}

.admin-isolated .textarea {
  min-height: 4rem;
  resize: vertical;
}

/* Simple Label Styles */
.admin-isolated .label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: hsl(var(--foreground));
  margin-bottom: 0.5rem;
}

/* Simple Table Styles */
.admin-isolated .table {
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
}

.admin-isolated .table-header {
  border-bottom: 1px solid hsl(var(--border));
}

.admin-isolated .table-row {
  border-bottom: 1px solid hsl(var(--border));
}

.admin-isolated .table-row:hover {
  background-color: hsl(var(--muted) / 0.5);
}

.admin-isolated .table-head {
  padding: 0.75rem;
  text-align: left;
  font-weight: 500;
  color: hsl(var(--muted-foreground));
  font-size: 0.875rem;
}

.admin-isolated .table-cell {
  padding: 0.75rem;
  color: hsl(var(--foreground));
  font-size: 0.875rem;
}

/* Simple Alert Styles */
.admin-isolated .alert {
  padding: 1rem;
  border: 1px solid hsl(var(--border));
  border-radius: 0.375rem;
  background-color: hsl(var(--card));
  margin-bottom: 1rem;
}

.admin-isolated .alert-destructive {
  border-color: hsl(var(--destructive));
  background-color: hsl(var(--destructive) / 0.1);
  color: hsl(var(--destructive));
}

.admin-isolated .alert-description {
  font-size: 0.875rem;
  margin: 0;
}

/* Simple Select Styles */
.admin-isolated .select {
  display: flex;
  width: 100%;
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  border: 1px solid hsl(var(--border));
  border-radius: 0.375rem;
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  cursor: pointer;
}

.admin-isolated .select:focus {
  outline: none;
  border-color: hsl(var(--ring));
  box-shadow: 0 0 0 2px hsl(var(--ring) / 0.2);
}

/* Simple Checkbox Styles */
.admin-isolated .checkbox {
  height: 1rem;
  width: 1rem;
  border-radius: 0.25rem;
  border: 1px solid hsl(var(--border));
  background-color: hsl(var(--background));
  cursor: pointer;
}

.admin-isolated .checkbox:checked {
  background-color: hsl(var(--primary));
  border-color: hsl(var(--primary));
}

.admin-isolated .checkbox:focus {
  outline: none;
  border-color: hsl(var(--ring));
  box-shadow: 0 0 0 2px hsl(var(--ring) / 0.2);
}

/* Simple Modal/Dialog Styles */
.admin-isolated .modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.admin-isolated .modal-content {
  background-color: hsl(var(--card));
  border: 1px solid hsl(var(--border));
  border-radius: var(--radius);
  padding: 1.5rem;
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
}

.admin-isolated .modal-header {
  margin-bottom: 1rem;
}

.admin-isolated .modal-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: hsl(var(--foreground));
  margin-bottom: 0.5rem;
}

.admin-isolated .modal-description {
  font-size: 0.875rem;
  color: hsl(var(--muted-foreground));
}

/* Form styling */
.admin-isolated .form-grid {
  display: grid;
  gap: 1.5rem;
}

.admin-isolated .form-section {
  background: hsl(var(--card));
  border: 1px solid hsl(var(--border));
  border-radius: var(--radius);
  padding: 1.5rem;
}

.admin-isolated .form-section-header {
  margin-bottom: 1rem;
}

.admin-isolated .form-section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: hsl(var(--foreground));
  margin-bottom: 0.25rem;
}

.admin-isolated .form-section-description {
  font-size: 0.875rem;
  color: hsl(var(--muted-foreground));
}

.admin-isolated .form-field {
  margin-bottom: 1rem;
}

.admin-isolated .form-field:last-child {
  margin-bottom: 0;
}

.admin-isolated .form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: hsl(var(--foreground));
  margin-bottom: 0.5rem;
}

.admin-isolated .character-count {
  font-size: 0.75rem;
  color: hsl(var(--muted-foreground));
  margin-top: 0.25rem;
}

/* Utility Classes */
.admin-isolated .space-y-2 > * + * {
  margin-top: 0.5rem;
}

.admin-isolated .space-y-3 > * + * {
  margin-top: 0.75rem;
}

.admin-isolated .space-y-4 > * + * {
  margin-top: 1rem;
}

.admin-isolated .space-y-6 > * + * {
  margin-top: 1.5rem;
}

.admin-isolated .space-x-2 > * + * {
  margin-left: 0.5rem;
}

.admin-isolated .space-x-4 > * + * {
  margin-left: 1rem;
}

.admin-isolated .flex {
  display: flex;
}

.admin-isolated .items-center {
  align-items: center;
}

.admin-isolated .justify-between {
  justify-content: space-between;
}

.admin-isolated .justify-start {
  justify-content: flex-start;
}

.admin-isolated .grid {
  display: grid;
}

.admin-isolated .grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.admin-isolated .grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

.admin-isolated .gap-4 {
  gap: 1rem;
}

.admin-isolated .gap-6 {
  gap: 1.5rem;
}

.admin-isolated .text-sm {
  font-size: 0.875rem;
}

.admin-isolated .text-xs {
  font-size: 0.75rem;
}

.admin-isolated .text-lg {
  font-size: 1.125rem;
}

.admin-isolated .text-xl {
  font-size: 1.25rem;
}

.admin-isolated .text-2xl {
  font-size: 1.5rem;
}

.admin-isolated .text-3xl {
  font-size: 1.875rem;
}

.admin-isolated .font-medium {
  font-weight: 500;
}

.admin-isolated .font-semibold {
  font-weight: 600;
}

.admin-isolated .font-bold {
  font-weight: 700;
}

.admin-isolated .text-center {
  text-align: center;
}

.admin-isolated .mb-4 {
  margin-bottom: 1rem;
}

.admin-isolated .mr-2 {
  margin-right: 0.5rem;
}

.admin-isolated .p-4 {
  padding: 1rem;
}

.admin-isolated .p-6 {
  padding: 1.5rem;
}

.admin-isolated .w-full {
  width: 100%;
}

.admin-isolated .max-w-md {
  max-width: 28rem;
}

.admin-isolated .min-h-screen {
  min-height: 100vh;
}

.admin-isolated .relative {
  position: relative;
}

.admin-isolated .absolute {
  position: absolute;
}

.admin-isolated .hidden {
  display: none;
}

/* Responsive utilities */
@media (min-width: 768px) {
  .admin-isolated .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .admin-isolated .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

@media (min-width: 1024px) {
  .admin-isolated .lg\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .admin-isolated .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .admin-isolated .lg\:col-span-2 {
    grid-column: span 2 / span 2;
  }
}
