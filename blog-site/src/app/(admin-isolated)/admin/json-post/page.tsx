'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { 
  Save, 
  Send, 
  ArrowLeft, 
  Code, 
  Eye, 
  CheckCircle, 
  AlertCircle,
  Copy,
  FileText
} from "lucide-react"
import Link from "next/link"

export default function JsonPostPage() {
  const router = useRouter()
  const [jsonInput, setJsonInput] = useState('')
  const [isValidJson, setIsValidJson] = useState(true)
  const [jsonError, setJsonError] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [previewData, setPreviewData] = useState<any>(null)
  const [showPreview, setShowPreview] = useState(false)

  // Example JSON structure - matches your original example
  const exampleJson = {
    "blogPost": {
      "metadata": {
        "title": "Dream Big and Shine Bright: Daily Motivation",
        "author": "Your Name",
        "category": "Motivation",
        "tags": ["motivation", "inspiration", "success", "personal-growth"],
        "publishDate": "2025-07-30",
        "status": "draft",
        "seoDescription": "Inspirational quotes and messages to motivate your daily journey towards success and happiness.",
        "readTime": "3 min read"
      },
      "content": {
        "introduction": {
          "text": "Start your day with powerful motivation and positive energy...",
          "image": {
            "url": "https://example.com/intro-image.jpg",
            "alt": "Motivational sunrise background",
            "caption": "Begin each day with purpose and passion"
          }
        },
        "sections": [
          {
            "id": 1,
            "type": "quote_card",
            "title": "Dream Big, Work Hard",
            "content": {
              "quotes": [
                {
                  "emoji": "✨",
                  "text": "Dream big,"
                },
                {
                  "emoji": "💪",
                  "text": "Work hard,"
                },
                {
                  "emoji": "🙏",
                  "text": "Stay humble,"
                },
                {
                  "emoji": "🔥",
                  "text": "Shine bright."
                }
              ],
              "backgroundStyle": {
                "backgroundColor": "#f0f8ff",
                "borderColor": "#4a90e2",
                "textColor": "#333"
              }
            },
            "image": {
              "url": "https://example.com/dream-big-image.jpg",
              "alt": "Person reaching for stars",
              "caption": "Reach for your dreams with determination"
            },
            "socialShare": {
              "enabled": true,
              "platforms": ["facebook", "whatsapp", "twitter"],
              "customMessage": "Dream big and work hard! 💪✨"
            }
          },
          {
            "id": 2,
            "type": "quote_card",
            "title": "Be Yourself",
            "content": {
              "quotes": [
                {
                  "emoji": "⭐",
                  "text": "Be yourself,"
                },
                {
                  "emoji": "💛",
                  "text": "Love fiercely,"
                },
                {
                  "emoji": "🚀",
                  "text": "Chase dreams,"
                },
                {
                  "emoji": "🌈",
                  "text": "Live freely."
                }
              ],
              "backgroundStyle": {
                "backgroundColor": "#fff9e6",
                "borderColor": "#ffa500",
                "textColor": "#333"
              }
            },
            "image": {
              "url": "https://example.com/be-yourself-image.jpg",
              "alt": "Person celebrating freedom",
              "caption": "Embrace your authentic self"
            },
            "socialShare": {
              "enabled": true,
              "platforms": ["facebook", "whatsapp", "twitter"],
              "customMessage": "Be yourself and love fiercely! 💛⭐"
            }
          },
          {
            "id": 3,
            "type": "quote_card",
            "title": "Hustle and Believe",
            "content": {
              "quotes": [
                {
                  "emoji": "💥",
                  "text": "Hustle in silence,"
                },
                {
                  "emoji": "🌸",
                  "text": "Let success shout,"
                },
                {
                  "emoji": "🌿",
                  "text": "Believe always,"
                },
                {
                  "emoji": "⚡",
                  "text": "Never doubt."
                }
              ],
              "backgroundStyle": {
                "backgroundColor": "#f0fff0",
                "borderColor": "#32cd32",
                "textColor": "#333"
              }
            },
            "image": {
              "url": "https://example.com/hustle-believe-image.jpg",
              "alt": "Success celebration",
              "caption": "Silent work, loud results"
            },
            "socialShare": {
              "enabled": true,
              "platforms": ["facebook", "whatsapp", "twitter"],
              "customMessage": "Hustle in silence, let success shout! 💥🌸"
            }
          },
          {
            "id": 4,
            "type": "quote_card",
            "title": "Spread Kindness",
            "content": {
              "quotes": [
                {
                  "emoji": "🌻",
                  "text": "Spread kindness,"
                },
                {
                  "emoji": "😊",
                  "text": "Choose joy,"
                },
                {
                  "emoji": "💙",
                  "text": "Stay grateful,"
                },
                {
                  "emoji": "🌱",
                  "text": "Be you."
                }
              ],
              "backgroundStyle": {
                "backgroundColor": "#fff0f5",
                "borderColor": "#ff69b4",
                "textColor": "#333"
              }
            },
            "image": {
              "url": "https://example.com/kindness-image.jpg",
              "alt": "Acts of kindness",
              "caption": "Small acts, big impact"
            },
            "socialShare": {
              "enabled": true,
              "platforms": ["facebook", "whatsapp", "twitter"],
              "customMessage": "Spread kindness and choose joy! 🌻😊"
            }
          }
        ],
        "conclusion": {
          "text": "Remember, every day is a new opportunity to grow, inspire, and make a positive impact...",
          "image": {
            "url": "https://example.com/conclusion-image.jpg",
            "alt": "Sunset with positive message",
            "caption": "End each day with gratitude and hope"
          }
        }
      },
      "media": {
        "featuredImage": {
          "url": "https://example.com/featured-image.jpg",
          "alt": "Motivational blog post featured image",
          "caption": "Daily motivation for success and happiness"
        },
        "gallery": [
          {
            "url": "https://example.com/gallery1.jpg",
            "alt": "Motivational quote 1",
            "caption": "Dream big and work hard"
          },
          {
            "url": "https://example.com/gallery2.jpg",
            "alt": "Motivational quote 2",
            "caption": "Be yourself and love fiercely"
          }
        ]
      },
      "engagement": {
        "commentsEnabled": true,
        "socialSharing": {
          "enabled": true,
          "platforms": ["facebook", "whatsapp", "twitter", "linkedin", "pinterest"],
          "customShareText": "Get your daily dose of motivation! 🌟"
        },
        "relatedPosts": [
          {
            "title": "Morning Motivation Rituals",
            "url": "/morning-motivation-rituals"
          },
          {
            "title": "Building Confidence Daily",
            "url": "/building-confidence-daily"
          }
        ]
      },
      "customization": {
        "layout": "card-style",
        "colorTheme": "motivational-bright",
        "fontStyle": "modern-clean",
        "animations": {
          "enabled": true,
          "type": "fade-in-on-scroll"
        }
      }
    }
  }

  const validateJson = (jsonString: string) => {
    try {
      const parsed = JSON.parse(jsonString)
      setIsValidJson(true)
      setJsonError('')
      setPreviewData(parsed)
      return true
    } catch (error) {
      setIsValidJson(false)
      setJsonError(error instanceof Error ? error.message : 'Invalid JSON format')
      setPreviewData(null)
      return false
    }
  }

  const handleJsonChange = (value: string) => {
    setJsonInput(value)
    if (value.trim()) {
      validateJson(value)
    } else {
      setIsValidJson(true)
      setJsonError('')
      setPreviewData(null)
    }
  }

  const handleSave = async (saveStatus: string) => {
    if (!jsonInput.trim()) {
      setJsonError('Please enter JSON data')
      return
    }

    if (!validateJson(jsonInput)) {
      return
    }

    setIsLoading(true)
    try {
      const response = await fetch('/api/admin/posts/json', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          jsonData: JSON.parse(jsonInput),
          status: saveStatus
        })
      })

      if (response.ok) {
        const result = await response.json()
        console.log('Post created:', result)
        router.push('/admin/posts')
      } else {
        const error = await response.json()
        setJsonError(error.message || 'Failed to create post')
      }
    } catch (error) {
      setJsonError('Failed to create post. Please try again.')
      console.error('Error creating post:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const copyExampleJson = () => {
    const jsonString = JSON.stringify(exampleJson, null, 2)
    navigator.clipboard.writeText(jsonString)
    setJsonInput(jsonString)
    validateJson(jsonString)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/admin/posts">
            <button className="btn btn-ghost btn-icon">
              <ArrowLeft className="h-4 w-4" />
            </button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-foreground">JSON Post Creator</h1>
            <p className="text-muted-foreground">Create blog posts using JSON input</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <button
            className="btn btn-outline"
            onClick={() => setShowPreview(!showPreview)}
            disabled={!previewData}
          >
            <Eye className="mr-2 h-4 w-4" />
            {showPreview ? 'Hide Preview' : 'Preview'}
          </button>
          <button
            className="btn btn-outline"
            onClick={() => handleSave('draft')}
            disabled={isLoading || !isValidJson}
          >
            <Save className="mr-2 h-4 w-4" />
            {isLoading ? 'Saving...' : 'Save Draft'}
          </button>
          <button
            className="btn btn-primary"
            onClick={() => handleSave('published')}
            disabled={isLoading || !isValidJson}
          >
            <Send className="mr-2 h-4 w-4" />
            {isLoading ? 'Publishing...' : 'Publish'}
          </button>
        </div>
      </div>

      <div className="grid gap-6 lg:grid-cols-2">
        {/* JSON Input */}
        <div className="space-y-4">
          <div className="card">
            <div className="card-header">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="card-title flex items-center">
                    <Code className="mr-2 h-4 w-4" />
                    JSON Input
                  </h3>
                  <p className="card-description">Paste your blog post JSON data here</p>
                </div>
                <button
                  className="btn btn-outline btn-sm"
                  onClick={copyExampleJson}
                >
                  <Copy className="mr-2 h-3 w-3" />
                  Use Example
                </button>
              </div>
            </div>
            <div className="card-content">
              <div className="space-y-2">
                <textarea
                  value={jsonInput}
                  onChange={(e) => handleJsonChange(e.target.value)}
                  placeholder="Paste your JSON data here..."
                  className={`input min-h-[400px] font-mono text-sm ${
                    !isValidJson ? 'border-red-500' : ''
                  }`}
                  style={{ resize: 'vertical' }}
                />
                {jsonError && (
                  <div className="flex items-center space-x-2 text-red-600 text-sm">
                    <AlertCircle className="h-4 w-4" />
                    <span>{jsonError}</span>
                  </div>
                )}
                {isValidJson && jsonInput && (
                  <div className="flex items-center space-x-2 text-green-600 text-sm">
                    <CheckCircle className="h-4 w-4" />
                    <span>Valid JSON format</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Preview/Instructions */}
        <div className="space-y-4">
          {showPreview && previewData ? (
            <div className="card">
              <div className="card-header">
                <h3 className="card-title flex items-center">
                  <Eye className="mr-2 h-4 w-4" />
                  Preview
                </h3>
                <p className="card-description">Preview of your blog post data</p>
              </div>
              <div className="card-content">
                <div className="space-y-4">
                  {previewData.blogPost?.metadata && (
                    <div>
                      <h4 className="font-semibold text-lg">
                        {previewData.blogPost.metadata.title}
                      </h4>
                      <p className="text-sm text-muted-foreground">
                        By {previewData.blogPost.metadata.author} • {previewData.blogPost.metadata.category}
                      </p>
                      <p className="text-sm mt-2">
                        {previewData.blogPost.metadata.seoDescription}
                      </p>
                      {previewData.blogPost.metadata.tags && (
                        <div className="flex flex-wrap gap-1 mt-2">
                          {previewData.blogPost.metadata.tags.map((tag: string, index: number) => (
                            <span key={index} className="badge badge-secondary text-xs">
                              {tag}
                            </span>
                          ))}
                        </div>
                      )}
                    </div>
                  )}
                  
                  {previewData.blogPost?.content?.sections && (
                    <div>
                      <h5 className="font-medium mb-2">Sections ({previewData.blogPost.content.sections.length})</h5>
                      <div className="space-y-2">
                        {previewData.blogPost.content.sections.map((section: any, index: number) => (
                          <div key={index} className="p-2 bg-accent rounded text-sm">
                            <strong>{section.title || `Section ${section.id}`}</strong>
                            <span className="text-muted-foreground ml-2">({section.type})</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ) : (
            <div className="card">
              <div className="card-header">
                <h3 className="card-title flex items-center">
                  <FileText className="mr-2 h-4 w-4" />
                  Instructions
                </h3>
                <p className="card-description">How to use the JSON Post Creator</p>
              </div>
              <div className="card-content">
                <div className="space-y-4 text-sm">
                  <div>
                    <h4 className="font-medium mb-2">1. JSON Structure</h4>
                    <p className="text-muted-foreground">
                      Your JSON should contain a "blogPost" object with "metadata" and "content" sections.
                    </p>
                  </div>
                  
                  <div>
                    <h4 className="font-medium mb-2">2. Required Fields</h4>
                    <ul className="list-disc list-inside text-muted-foreground space-y-1">
                      <li>metadata.title - Post title</li>
                      <li>metadata.author - Author name</li>
                      <li>metadata.category - Post category</li>
                      <li>content.sections - Array of content sections</li>
                    </ul>
                  </div>
                  
                  <div>
                    <h4 className="font-medium mb-2">3. Quick Start</h4>
                    <p className="text-muted-foreground">
                      Click "Use Example" to load a sample JSON structure that you can modify.
                    </p>
                  </div>
                  
                  <div>
                    <h4 className="font-medium mb-2">4. Validation</h4>
                    <p className="text-muted-foreground">
                      The system will validate your JSON format and show any errors below the input area.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
