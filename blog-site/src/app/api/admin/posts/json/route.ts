import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/database'

// Define the expected JSON structure
interface BlogPostJson {
  blogPost: {
    metadata: {
      title: string
      author: string
      category: string
      tags?: string[]
      publishDate?: string
      status?: string
      seoDescription?: string
      readTime?: string
    }
    content: {
      introduction?: {
        text: string
        image?: {
          url: string
          alt: string
          caption?: string
        }
      }
      sections: Array<{
        id: number
        type: string
        title: string
        content: any
        image?: {
          url: string
          alt: string
          caption?: string
        }
        socialShare?: {
          enabled: boolean
          platforms: string[]
          customMessage?: string
        }
      }>
      conclusion?: {
        text: string
        image?: {
          url: string
          alt: string
          caption?: string
        }
      }
    }
    media?: {
      featuredImage?: {
        url: string
        alt: string
        caption?: string
      }
      gallery?: Array<{
        url: string
        alt: string
        caption?: string
      }>
    }
    engagement?: {
      commentsEnabled?: boolean
      socialSharing?: {
        enabled: boolean
        platforms: string[]
        customShareText?: string
      }
      relatedPosts?: Array<{
        title: string
        url: string
      }>
    }
    customization?: {
      layout?: string
      colorTheme?: string
      fontStyle?: string
      animations?: {
        enabled: boolean
        type: string
      }
    }
  }
}

function generateSlug(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
    .trim()
}

function convertJsonToHtml(jsonData: BlogPostJson): string {
  const { blogPost } = jsonData
  let html = ''

  // Add introduction
  if (blogPost.content.introduction) {
    html += `<div class="post-introduction">`
    html += `<p>${blogPost.content.introduction.text}</p>`
    if (blogPost.content.introduction.image) {
      html += `<div class="intro-image">
        <img src="${blogPost.content.introduction.image.url}" alt="${blogPost.content.introduction.image.alt}" />
        ${blogPost.content.introduction.image.caption ? `<p class="image-caption">${blogPost.content.introduction.image.caption}</p>` : ''}
      </div>`
    }
    html += `</div>`
  }

  // Add sections
  if (blogPost.content.sections) {
    blogPost.content.sections.forEach(section => {
      html += `<div class="post-section" data-section-id="${section.id}" data-section-type="${section.type}">`
      html += `<h3 class="section-title">${section.title}</h3>`

      if (section.type === 'quote_card' && section.content.quotes) {
        // Apply background styles if available
        const backgroundStyle = section.content.backgroundStyle
        const styleAttr = backgroundStyle ?
          `style="background-color: ${backgroundStyle.backgroundColor}; border-color: ${backgroundStyle.borderColor}; color: ${backgroundStyle.textColor};"` : ''

        html += `<div class="quote-card" ${styleAttr}>`
        section.content.quotes.forEach((quote: any) => {
          html += `<div class="quote-item">
            <span class="quote-emoji">${quote.emoji}</span>
            <span class="quote-text">${quote.text}</span>
          </div>`
        })
        html += `</div>`

        // Add social sharing if enabled
        if (section.socialShare?.enabled) {
          html += `<div class="social-share" data-platforms="${section.socialShare.platforms.join(',')}" data-message="${section.socialShare.customMessage || ''}">
            <p class="share-text">Share this quote:</p>
            <div class="share-buttons">
              ${section.socialShare.platforms.map(platform =>
                `<button class="share-btn share-${platform}" data-platform="${platform}">${platform}</button>`
              ).join('')}
            </div>
          </div>`
        }
      } else if (section.content.text) {
        html += `<div class="section-content">${section.content.text}</div>`
      }

      if (section.image) {
        html += `<div class="section-image">
          <img src="${section.image.url}" alt="${section.image.alt}" />
          ${section.image.caption ? `<p class="image-caption">${section.image.caption}</p>` : ''}
        </div>`
      }

      html += `</div>`
    })
  }

  // Add conclusion
  if (blogPost.content.conclusion) {
    html += `<div class="post-conclusion">`
    html += `<p>${blogPost.content.conclusion.text}</p>`
    if (blogPost.content.conclusion.image) {
      html += `<div class="conclusion-image">
        <img src="${blogPost.content.conclusion.image.url}" alt="${blogPost.content.conclusion.image.alt}" />
        ${blogPost.content.conclusion.image.caption ? `<p class="image-caption">${blogPost.content.conclusion.image.caption}</p>` : ''}
      </div>`
    }
    html += `</div>`
  }

  // Add engagement features
  if (blogPost.engagement) {
    html += `<div class="post-engagement">`

    if (blogPost.engagement.socialSharing?.enabled) {
      html += `<div class="post-social-sharing">
        <h4>Share this post:</h4>
        <div class="social-buttons">
          ${blogPost.engagement.socialSharing.platforms.map(platform =>
            `<button class="social-btn social-${platform}" data-platform="${platform}">${platform}</button>`
          ).join('')}
        </div>
      </div>`
    }

    if (blogPost.engagement.relatedPosts && blogPost.engagement.relatedPosts.length > 0) {
      html += `<div class="related-posts">
        <h4>Related Posts:</h4>
        <ul>
          ${blogPost.engagement.relatedPosts.map(post =>
            `<li><a href="${post.url}">${post.title}</a></li>`
          ).join('')}
        </ul>
      </div>`
    }

    html += `</div>`
  }

  return html
}

function generateExcerpt(jsonData: BlogPostJson): string {
  const { blogPost } = jsonData
  
  // Try to get excerpt from introduction
  if (blogPost.content.introduction?.text) {
    return blogPost.content.introduction.text.substring(0, 200) + '...'
  }
  
  // Try to get excerpt from SEO description
  if (blogPost.metadata.seoDescription) {
    return blogPost.metadata.seoDescription
  }
  
  // Try to get excerpt from first section
  if (blogPost.content.sections && blogPost.content.sections.length > 0) {
    const firstSection = blogPost.content.sections[0]
    if (firstSection.content.text) {
      return firstSection.content.text.substring(0, 200) + '...'
    }
    if (firstSection.content.quotes && firstSection.content.quotes.length > 0) {
      return firstSection.content.quotes.map((q: any) => q.text).join(' ').substring(0, 200) + '...'
    }
  }
  
  return blogPost.metadata.title
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { jsonData, status = 'draft' } = body

    if (!jsonData || !jsonData.blogPost) {
      return NextResponse.json(
        { error: 'Invalid JSON structure. Expected blogPost object.' },
        { status: 400 }
      )
    }

    const { blogPost } = jsonData as BlogPostJson

    // Validate required fields
    if (!blogPost.metadata?.title) {
      return NextResponse.json(
        { error: 'Missing required field: metadata.title' },
        { status: 400 }
      )
    }

    if (!blogPost.metadata?.author) {
      return NextResponse.json(
        { error: 'Missing required field: metadata.author' },
        { status: 400 }
      )
    }

    if (!blogPost.content?.sections || blogPost.content.sections.length === 0) {
      return NextResponse.json(
        { error: 'Missing required field: content.sections' },
        { status: 400 }
      )
    }

    // Generate slug from title
    const slug = generateSlug(blogPost.metadata.title)
    
    // Convert JSON to HTML content
    const htmlContent = convertJsonToHtml(jsonData)
    
    // Generate excerpt
    const excerpt = generateExcerpt(jsonData)
    
    // Get featured image URL
    const featuredImageUrl = blogPost.media?.featuredImage?.url || null
    
    // Prepare post data
    const postData = {
      title: blogPost.metadata.title,
      slug: slug,
      content: htmlContent,
      excerpt: excerpt,
      author_id: 1, // Default admin user
      status: status,
      post_type: 'post',
      featured_image_url: featuredImageUrl,
      meta_title: blogPost.metadata.title,
      meta_description: blogPost.metadata.seoDescription || excerpt,
      language: 'hi', // Default to Hindi
      view_count: 0,
      comment_count: 0,
      published_at: status === 'published' ? new Date().toISOString() : null,
      // Store original JSON data as metadata
      json_metadata: JSON.stringify(jsonData)
    }

    // Create the post using database service
    const result = await db.createPost(postData)

    return NextResponse.json({
      success: true,
      message: 'Post created successfully',
      post: result
    })

  } catch (error) {
    console.error('Error creating post from JSON:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
